name: haimed_getx
version: 2.0.0+1
publish_to: none
description: A new Flutter project.
environment:
  sdk: ">=3.0.6 <=4.0.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  get: 4.6.6
  http: ^1.1.0
  font_awesome_flutter: ^10.3.0
  flutter_easyloading: ^3.0.5
  modal_bottom_sheet: ^3.0.0-pre
  google_fonts: ^6.1.0
  shimmer: ^3.0.0
  firebase_auth: ^5.3.4
  firebase_messaging: ^15.1.6
  firebase_remote_config: ^5.2.0
  firebase_analytics: ^11.3.6
  get_storage: ^2.1.1
  overlay_support: ^2.1.0
  package_info_plus: ^7.0.0
  uuid: ^4.4.0
  intl: ^0.19.0
  firebase_core: ^3.8.1
  google_sign_in: ^6.3.0
  sign_in_with_apple: ^6.1.0
  flutter_local_notifications: ^18.0.1
  crypto: ^3.0.2
  introduction_screen: ^3.1.4
  carousel_slider: ^4.2.1
  cloud_firestore: ^4.2.0
  table_calendar: ^3.0.8
  rating_summary: ^1.0.1+1
  flutter_rating_bar: ^4.0.1
  flutter_datetime_picker_plus: ^2.1.0
  timeline_tile: ^2.0.0
  qr_flutter: ^4.0.0
  intl_phone_number_input: ^0.7.4
  flutter_otp_text_field: ^1.1.3+2
  image_picker: ^1.1.2
  firebase_storage: ^11.7.2
  flutter_rating_stars: ^1.0.3+4
  url_launcher: ^6.1.10
  coachmaker: ^0.2.1
  flutter_widget_from_html: ^0.14.11
  share_plus: ^8.0.3

dev_dependencies:
  flutter_launcher_icons: ^0.13.1
  flutter_lints: ^3.0.2
  lints: ^3.0.0
  flutter_test:
    sdk: flutter

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/images/cendana/
    - assets/images/rsbk/
    - assets/images/premagana/
    - assets/images/suwiti/
    - assets/images/giriasih/
    - assets/icons/

  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/poppins/Poppins-Thin.ttf
          weight: 100
        - asset: assets/fonts/poppins/Poppins-ThinItalic.ttf
          weight: 100
          style: italic
        - asset: assets/fonts/poppins/Poppins-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/poppins/Poppins-ExtraLightItalic.ttf
          weight: 200
          style: italic
        - asset: assets/fonts/poppins/Poppins-Light.ttf
          weight: 300
        - asset: assets/fonts/poppins/Poppins-LightItalic.ttf
          weight: 300
          style: italic
        - asset: assets/fonts/poppins/Poppins-Regular.ttf
          weight: 400
        - asset: assets/fonts/poppins/Poppins-Italic.ttf
          weight: 400
          style: italic
        - asset: assets/fonts/poppins/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/poppins/Poppins-MediumItalic.ttf
          weight: 500
          style: italic
        - asset: assets/fonts/poppins/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/poppins/Poppins-SemiBoldItalic.ttf
          weight: 600
          style: italic
        - asset: assets/fonts/poppins/Poppins-Bold.ttf
          weight: 700
        - asset: assets/fonts/poppins/Poppins-BoldItalic.ttf
          weight: 700
          style: italic
        - asset: assets/fonts/poppins/Poppins-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/poppins/Poppins-ExtraBoldItalic.ttf
          weight: 800
          style: italic
        - asset: assets/fonts/poppins/Poppins-Black.ttf
          weight: 900
        - asset: assets/fonts/poppins/Poppins-BlackItalic.ttf
          weight: 900
          style: italic
