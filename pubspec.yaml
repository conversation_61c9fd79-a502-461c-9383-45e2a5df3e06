name: haimed_getx
version: 2.0.0+1
publish_to: none
description: A new Flutter project.
environment:
  sdk: ">=3.0.6 <=4.0.0"

dependencies:
  # Flutter SDK
  flutter:
    sdk: flutter

  # Core Framework & State Management
  get: 4.6.6

  # Firebase Services
  cloud_firestore: ^5.6.10
  firebase_analytics: ^11.3.6
  firebase_auth: ^5.3.4
  firebase_core: ^3.8.1
  firebase_messaging: ^15.1.6
  firebase_remote_config: ^5.2.0
  firebase_storage: ^12.4.8

  # Authentication & Sign-in
  google_sign_in: ^6.3.0
  sign_in_with_apple: ^6.1.0

  # UI Components & Icons
  carousel_slider: ^5.1.1
  cupertino_icons: ^1.0.2
  font_awesome_flutter: ^10.3.0
  flutter_datetime_picker_plus: ^2.1.0
  flutter_easyloading: ^3.0.5
  flutter_otp_text_field: ^1.1.3+2
  flutter_rating_bar: ^4.0.1
  flutter_rating_stars: ^1.0.3+4
  flutter_widget_from_html: ^0.14.11
  google_fonts: ^6.1.0
  introduction_screen: ^3.1.4
  modal_bottom_sheet: ^3.0.0-pre
  overlay_support: ^2.1.0
  qr_flutter: ^4.0.0
  rating_summary: ^1.0.1+1
  shimmer: ^3.0.0
  table_calendar: ^3.0.8
  timeline_tile: ^2.0.0

  # Media & File Handling
  image_picker: ^1.1.2

  # Networking & HTTP
  http: ^1.1.0

  # Notifications
  flutter_local_notifications: ^18.0.1

  # Storage & Data Persistence
  get_storage: ^2.1.1

  # Utilities & Helpers
  coachmaker: ^0.2.1
  crypto: ^3.0.2
  intl: ^0.19.0
  intl_phone_number_input: ^0.7.4
  package_info_plus: ^8.0.0
  share_plus: ^11.0.0
  url_launcher: ^6.1.10
  uuid: ^4.4.0

dev_dependencies:
  flutter_launcher_icons: ^0.13.1
  flutter_lints: ^3.0.2
  lints: ^3.0.0
  flutter_test:
    sdk: flutter

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/images/cendana/
    - assets/images/rsbk/
    - assets/images/premagana/
    - assets/images/suwiti/
    - assets/images/giriasih/
    - assets/icons/

  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/poppins/Poppins-Thin.ttf
          weight: 100
        - asset: assets/fonts/poppins/Poppins-ThinItalic.ttf
          weight: 100
          style: italic
        - asset: assets/fonts/poppins/Poppins-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/poppins/Poppins-ExtraLightItalic.ttf
          weight: 200
          style: italic
        - asset: assets/fonts/poppins/Poppins-Light.ttf
          weight: 300
        - asset: assets/fonts/poppins/Poppins-LightItalic.ttf
          weight: 300
          style: italic
        - asset: assets/fonts/poppins/Poppins-Regular.ttf
          weight: 400
        - asset: assets/fonts/poppins/Poppins-Italic.ttf
          weight: 400
          style: italic
        - asset: assets/fonts/poppins/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/poppins/Poppins-MediumItalic.ttf
          weight: 500
          style: italic
        - asset: assets/fonts/poppins/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/poppins/Poppins-SemiBoldItalic.ttf
          weight: 600
          style: italic
        - asset: assets/fonts/poppins/Poppins-Bold.ttf
          weight: 700
        - asset: assets/fonts/poppins/Poppins-BoldItalic.ttf
          weight: 700
          style: italic
        - asset: assets/fonts/poppins/Poppins-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/poppins/Poppins-ExtraBoldItalic.ttf
          weight: 800
          style: italic
        - asset: assets/fonts/poppins/Poppins-Black.ttf
          weight: 900
        - asset: assets/fonts/poppins/Poppins-BlackItalic.ttf
          weight: 900
          style: italic
