#packages: flutter_riverpod
@@@lib/presentation/hyper_example/view/hyper_example_view.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hyper_ui/core.dart';

final hyperExampleProvider = StateNotifierProvider<HyperExampleController, HyperExampleState>((ref) {
  return HyperExampleController();
});

class HyperExampleView extends ConsumerStatefulWidget {
  const HyperExampleView({super.key});

  @override
  ConsumerState<HyperExampleView> createState() => _HyperExampleViewState();
}

class _HyperExampleViewState extends ConsumerState<HyperExampleView> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => onReady());
  }

  void onReady() {
    // After 1st build() is called
    ref.read(hyperExampleProvider.notifier).initializeData();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(hyperExampleProvider);

    if (state.status == HyperExampleStatus.loading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (state.status == HyperExampleStatus.error) {
      return Scaffold(
        body: Center(
          child: Text("Error: ${state.errorMessage}"),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text("HyperExample"),
        actions: const [],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            Text(
              "UniqueID: ${UniqueKey()}",
              style: const TextStyle(
                fontSize: 18.0,
              ),
            ),
            const SizedBox(
              height: 12.0,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: () => ref.read(hyperExampleProvider.notifier).decrement(),
                  icon: const Icon(Icons.remove, color: Colors.grey),
                ),
                Text(
                  "${state.counter}",
                  style: const TextStyle(
                    fontSize: 20.0,
                    color: Colors.grey,
                  ),
                ),
                IconButton(
                  onPressed: () => ref.read(hyperExampleProvider.notifier).increment(),
                  icon: const Icon(Icons.add, color: Colors.grey),
                ),
              ],
            ),
            const SizedBox(
              height: 12.0,
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.black,
                foregroundColor: Colors.white,
              ),
              onPressed: () => ref.read(hyperExampleProvider.notifier).initializeData(),
              child: const Text("Reload"),
            ),
          ],
        ),
      ),
    );
  }
}
---
@@@lib/presentation/hyper_example/state/hyper_example_state.dart
enum HyperExampleStatus { initial, loading, loaded, error }

class HyperExampleState {
  final HyperExampleStatus status;
  final int counter;
  final String errorMessage;

  HyperExampleState({
    this.status = HyperExampleStatus.initial,
    this.counter = 0,
    this.errorMessage = '',
  });

  HyperExampleState copyWith({
    HyperExampleStatus? status,
    int? counter,
    String? errorMessage,
  }) {
    return HyperExampleState(
      status: status ?? this.status,
      counter: counter ?? this.counter,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
---
@@@lib/presentation/hyper_example/controller/hyper_example_controller.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hyper_ui/core.dart';

class HyperExampleController extends StateNotifier<HyperExampleState> {
  HyperExampleController() : super(HyperExampleState());

  Future<void> initializeData() async {
    state = state.copyWith(status: HyperExampleStatus.loading);
    
    try {
      await Future.delayed(const Duration(seconds: 2));
      state = state.copyWith(
        status: HyperExampleStatus.loaded,
      );
    } catch (e) {
      state = state.copyWith(
        status: HyperExampleStatus.error,
        errorMessage: e.toString(),
      );
    }
  }

  void increment() {
    state = state.copyWith(counter: state.counter + 1);
  }

  void decrement() {
    state = state.copyWith(counter: state.counter - 1);
  }
}
---
@@@lib/presentation/hyper_example/widget/_
---