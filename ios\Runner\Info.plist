<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDisplayName</key>
	<string>$(APP_DISPLAY_NAME)</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>freddy</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>sms</string>
			<string>tel</string>
		</array>
		<key>NSCameraUsageDescription  </key>
		<string>This app require permission for accessing camera</string>
		<key>NSMicrophoneUsageDescription  </key>
		<string>This app require permission for accessing microphone</string>
		<key>NSPhotoLibraryUsageDescription </key>
		<string>This app require permission for accessing gallery</string>
	<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.googleusercontent.apps.1026284748792-o4cgpdtn2bvs41idjqu3bamcro6q9b1r</string>
					<string>com.googleusercontent.apps.22523139239-2nf13t63ttakm79g54kcd9shvh7ntpod</string>
					<string>com.googleusercontent.apps.12506517632-nvg17l341gundti2o5mhtbjeq6sepf35</string>
					<string>com.googleusercontent.apps.839320340926-lg0g0uk8n0k1v3jug371c8c7haf5dfb2</string>
					<string>com.googleusercontent.apps.894116160966-hqlvtov12psq8u4jo9ojujtoqjp0uhko</string>
					<string>com.googleusercontent.apps.664352997248-mano87hqkc64okdtmd88in0kbetplvls</string>
				</array>
			</dict>
		</array>
</dict>
</plist>
