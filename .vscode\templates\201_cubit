#packages: flutter_bloc bloc get_it
@@@lib/presentation/hyper_example/view/hyper_example_view.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:hyper_ui/core.dart';

class HyperExampleView extends StatefulWidget {
  const HyperExampleView({super.key});

  @override
  State<HyperExampleView> createState() => _HyperExampleViewState();
}

class _HyperExampleViewState extends State<HyperExampleView> {
  late HyperExampleCubit cubit;

  @override
  void initState() {
    super.initState();
    cubit = GetIt.I<HyperExampleCubit>();
    WidgetsBinding.instance.addPostFrameCallback((_) => onReady());
  }

  void onReady() {
    // After 1st build() is called
    cubit.initialize();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => cubit,
      child: HyperExampleListener(
        child: <PERSON><PERSON><PERSON>er<HyperExampleCubit, HyperExampleState>(
          builder: (context, state) {
            if (state.status == HyperExampleStatus.loading) {
              return const Scaffold(
                body: Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }

            if (state.status == HyperExampleStatus.error) {
              return Scaffold(
                body: Center(
                  child: Text("Error: ${state.errorMessage}"),
                ),
              );
            }

            return Scaffold(
              appBar: AppBar(
                title: const Text("HyperExample"),
                actions: const [],
              ),
              body: SingleChildScrollView(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    Text(
                      "UniqueID: ${UniqueKey()}",
                      style: const TextStyle(
                        fontSize: 18.0,
                      ),
                    ),
                    const SizedBox(
                      height: 12.0,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton(
                          onPressed: () => context.read<HyperExampleCubit>().decrement(),
                          icon: const Icon(Icons.remove, color: Colors.grey),
                        ),
                        Text(
                          "${state.counter}",
                          style: const TextStyle(
                            fontSize: 20.0,
                            color: Colors.grey,
                          ),
                        ),
                        IconButton(
                          onPressed: () => context.read<HyperExampleCubit>().increment(),
                          icon: const Icon(Icons.add, color: Colors.grey),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 12.0,
                    ),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.black,
                        foregroundColor: Colors.white,
                      ),
                      onPressed: () => context.read<HyperExampleCubit>().initialize(),
                      child: const Text("Reload"),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
---
@@@lib/presentation/hyper_example/state/hyper_example_state.dart
enum HyperExampleStatus { initial, loading, loaded, error }

class HyperExampleState {
  final HyperExampleStatus status;
  final int counter;
  final String errorMessage;

  HyperExampleState({
    this.status = HyperExampleStatus.initial,
    this.counter = 0,
    this.errorMessage = '',
  });

  HyperExampleState copyWith({
    HyperExampleStatus? status,
    int? counter,
    String? errorMessage,
  }) {
    return HyperExampleState(
      status: status ?? this.status,
      counter: counter ?? this.counter,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
---
@@@lib/presentation/hyper_example/cubit/hyper_example_cubit.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hyper_ui/core.dart';

class HyperExampleCubit extends Cubit<HyperExampleState> {
  HyperExampleCubit() : super(HyperExampleState());

  Future<void> initialize() async {
    emit(state.copyWith(status: HyperExampleStatus.loading));
    
    try {
      await Future.delayed(const Duration(seconds: 2));
      emit(state.copyWith(
        status: HyperExampleStatus.loaded,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: HyperExampleStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  void increment() {
    emit(state.copyWith(counter: state.counter + 1));
  }

  void decrement() {
    emit(state.copyWith(counter: state.counter - 1));
  }
}
---
@@@lib/presentation/hyper_example/listener/hyper_example_listener.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hyper_ui/core.dart';

class HyperExampleListener extends StatelessWidget {
  final Widget child;
  
  const HyperExampleListener({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocListener<HyperExampleCubit, HyperExampleState>(
      listener: (context, state) {
        if (state.status == HyperExampleStatus.loading) {
          // Handle loading state if needed
          // For example, show a loading dialog
        }
        
        if (state.status == HyperExampleStatus.error) {
          // Handle error state
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage),
              backgroundColor: Colors.red,
            ),
          );
        }
        
        if (state.status == HyperExampleStatus.loaded) {
          // Handle loaded state if needed
          // For example, show a success message
        }
      },
      child: child,
    );
  }
}
---
@@@lib/presentation/hyper_example/widget/_
---