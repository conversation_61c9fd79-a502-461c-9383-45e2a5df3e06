{"project_info": {"project_number": "1026284748792", "project_id": "ha<PERSON>-c<PERSON>ana", "storage_bucket": "haimed-cendana.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:1026284748792:android:be3a0609d6d601d75da00d", "android_client_info": {"package_name": "com.sanata.haimed.cendana"}}, "oauth_client": [{"client_id": "1026284748792-blu8rqnbsmlaubn83plvku1dlf93sfco.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.haimed.cendana", "certificate_hash": "d3a82e3a3b61abffad880df6cef8e3b9e537c4d6"}}, {"client_id": "1026284748792-k52pdiobe26etqfoa7kgkp4dllj5t9hu.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.haimed.cendana", "certificate_hash": "0b493fc0ca2b0c2dc45a8786c0e82240ffb5963e"}}, {"client_id": "1026284748792-mbqo0dobni0kgv61tho5fhddt8kvu05l.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.haimed.cendana", "certificate_hash": "d1a58c92845af7a4267a47d1aea3d3e3ef0012fa"}}, {"client_id": "1026284748792-oheuhfjvd6merd5ma4ipqa68kljq2fpr.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.haimed.cendana", "certificate_hash": "5785a3ff042b47392efd3800052ca6c07b733b81"}}, {"client_id": "1026284748792-qjis86b4ukecti23c4gegtkmoviss5bi.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.haimed.cendana", "certificate_hash": "08284361b5d5369acc057f00bd7f1208f2c07edc"}}, {"client_id": "1026284748792-uirl58lcl24u1k7vfk21bd860j1lpel7.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.haimed.cendana", "certificate_hash": "8b5d2a581ca3cf97a5c68c954be5a000d583d7ae"}}, {"client_id": "1026284748792-vpe2b768qgf7dti8atdkrmt99osml6pv.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.haimed.cendana", "certificate_hash": "ffad9ea0a45ef2ff20fdf1b4c980ff1a2c2bb2bb"}}, {"client_id": "1026284748792-lt88r9jh5sq0enr39o1afef9tlk6p3ta.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDE9Mfj4sNZdHaN22N8v6peWPbrmGLaTeQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "1026284748792-99ae1j1c2ak2iql1tpfo3bo7tmep9f57.apps.googleusercontent.com", "client_type": 3}, {"client_id": "1026284748792-ojjf6k6cjek3jlvupfcibk4bp7vlchb3.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.sanata.haimedGetx"}}]}}}], "configuration_version": "1"}