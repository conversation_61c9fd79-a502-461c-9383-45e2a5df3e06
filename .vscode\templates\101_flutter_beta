#router_generator
#sl_generator
#sl_controller
#packages: get_it auto_route
@@@lib/presentation/hyper_example/view/hyper_example_view.dart
import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';
import 'package:hyper_ui/core.dart';

@RoutePage()
class HyperExampleView extends StatefulWidget {
  const HyperExampleView({super.key});

  @override
  State<HyperExampleView> createState() => _HyperExampleViewState();
}

class _HyperExampleViewState extends State<HyperExampleView> {
  final controller = sl<HyperExampleController>();
  HyperExampleState get state => controller.state;

  @override
  void initState() {
    super.initState();
    controller.initState(init: () {
      //after state is initialized
    });
    WidgetsBinding.instance.addPostFrameCallback((_) => onReady());
  }

  void onReady() {
    //after 1st build() is called
    //an example of how to listen to ValueNotifier
    controller.state.error.addListener(() {
      controller.onReady();
      // handle loading state
      // you can handle navigation, dialog, snackbar, etc
      // based on the loading state
      if (controller.state.error.value) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(controller.state.errorMessage.value),
          ),
        );
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    controller.dispose();
  }


  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: state.loading,
      builder: (context, __, _) {
        if (state.loading.value) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: const Text("HyperExample"),
            actions: const [],
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                Text(
                  "UniqueID: ${UniqueKey()}",
                  style: const TextStyle(
                    fontSize: 18.0,
                  ),
                ),
                const SizedBox(
                  height: 12.0,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      onPressed: controller.decrement,
                      icon: const Icon(Icons.remove, color: Colors.grey),
                    ),
                    ValueListenableBuilder(
                      valueListenable: state.counter,
                      builder: (context, __, _) {
                        return Text(
                          "${state.counter.value}",
                          style: const TextStyle(
                            fontSize: 20.0,
                            color: Colors.grey,
                          ),
                        );
                      },
                    ),
                    IconButton(
                      onPressed: controller.increment,
                      icon: const Icon(Icons.add, color: Colors.grey),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 12.0,
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.black,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: () => controller.initializeData(),
                  child: const Text("Reload"),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

---
@@@lib/presentation/hyper_example/state/hyper_example_state.dart
import 'package:hyper_ui/core.dart';
import 'package:flutter/material.dart';

class HyperExampleState {
  ValueNotifier<bool> loading = ValueNotifier<bool>(false);
  ValueNotifier<bool> error = ValueNotifier<bool>(false);
  ValueNotifier<String> errorMessage = ValueNotifier<String>("");
  ValueNotifier<int> counter = ValueNotifier<int>(0);
}


---
@@@lib/presentation/hyper_example/controller/hyper_example_controller.dart
import 'package:hyper_ui/core.dart';

abstract class HyperExampleController {
  late HyperExampleState state;

  void initState({
    required Function init,
  });

  void onReady() {}
  void dispose() {}
  void initializeData();
  void increment();
  void decrement();
}
---
@@@lib/presentation/hyper_example/controller/hyper_example_controller_impl.dart
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:hyper_ui/core.dart';

class HyperExampleControllerImpl implements HyperExampleController {
  @override
  late HyperExampleState state;

  @override
  void initState({
    required Function init,
  }) {
    state = HyperExampleState();
    init.call();
  }

  @override
  void onReady() {
    //handle onReady
  }

  @override
  void dispose() {
    //handle dispose
  }

  @override
  void initializeData() async {
    state.loading.value = true;
    await Future.delayed(const Duration(milliseconds: 2000));
    state.loading.value = false;
  }

  @override
  void increment() {
    state.counter.value++;
  }

  @override
  void decrement() {
    state.counter.value--;
  }
}

---
@@@lib/presentation/hyper_example/widget/_
---