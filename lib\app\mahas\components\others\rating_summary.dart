library rating_summary;

import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';

class RatingSummary extends StatelessWidget {
  const RatingSummary({
    super.key,
    required this.counter,
    this.average = 0.0,
    this.showAverage = true,
    this.averageStyle = const TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 40,
    ),
    this.counterFiveStars = 0,
    this.counterFourStars = 0,
    this.counterThreeStars = 0,
    this.counterTwoStars = 0,
    this.counterOneStars = 0,
    this.label = 'Total',
    this.labelStyle = const TextStyle(fontWeight: FontWeight.w600),
    this.color = Colors.amber,
    this.backgroundColor = const Color(0xFFEEEEEE),
  });

  /// The total number of ratings.
  ///
  /// This is the number of ratings that will be displayed in the [label].
  final int counter;

  /// The average rating.
  ///
  /// This is the average rating that will be displayed when [showAverage] is true.
  /// You can use the [averageStyle] to customize the look of the average rating.
  /// The default value is 0.0.
  /// This value will NOT be calculated from the given parameters.
  final double average;

  /// Whether to show the average rating.
  ///
  /// If true, the average rating will be displayed. If not specified, the default value is true.
  final bool showAverage;

  /// The style of the [average] rating.
  ///
  /// You can use this to customize the look of it.
  /// The default value is a bold font size of 40.
  final TextStyle averageStyle;

  /// The number of ratings with 5 stars.
  ///
  /// This is the number of ratings with 5 stars that will be displayed in the [_ReviewBar].
  /// The default value is 0.
  final int counterFiveStars;

  /// The number of ratings with 4 stars.
  ///
  /// This is the number of ratings with 4 stars that will be displayed in the [_ReviewBar].
  /// The default value is 0.
  final int counterFourStars;

  /// The number of ratings with 3 stars.
  ///
  /// This is the number of ratings with 3 stars that will be displayed in the [_ReviewBar].
  /// The default value is 0.
  final int counterThreeStars;

  /// The number of ratings with 2 stars.
  ///
  /// This is the number of ratings with 2 stars that will be displayed in the [_ReviewBar].
  /// The default value is 0.
  final int counterTwoStars;

  /// The number of ratings with 1 stars.
  ///
  /// This is the number of ratings with 1 stars that will be displayed in the [_ReviewBar].
  final int counterOneStars;

  /// The label of the [counter]
  ///
  /// It will be displayed below the [average] counter with stars. It only appears when [showAverage] is true.
  final String label;

  /// The style of the [label].
  ///
  /// You can use this to customize the look of it. The default value is a semi-bold font.
  final TextStyle labelStyle;

  /// The color of the stars and the horizontal bar [_ReviewBar].
  ///
  /// The default value is Colors.amber.
  final Color color;

  /// The color of the unused stars and the background of the horizontal bar [_ReviewBar].
  ///
  /// The default value is Color(0xFFEEEEEE).
  final Color backgroundColor;

  @override
  Widget build(BuildContext context) {
    return Row(
      // mainAxisSize: MainAxisSize.min,
      children: [
        if (showAverage) ...[
          Flexible(
            child: Column(
              // mainAxisSize: MainAxisSize.min,
              children: [
                Text(average.toStringAsFixed(1), style: averageStyle),
                RatingBarIndicator(
                  rating: average,
                  itemSize: 28,
                  unratedColor: backgroundColor,
                  itemBuilder: (context, index) {
                    return Icon(Icons.star, color: color);
                  },
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.people,
                      size: 24.0,
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Text(
                      "$counter $label",
                      style: const TextStyle(fontWeight: FontWeight.w600),
                      overflow: TextOverflow.fade,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
        SizedBox(
          width: 30,
        ),
        Flexible(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _ReviewBar(
                label: "5",
                value: counterFiveStars / counter,
                color: color,
                backgroundColor: backgroundColor,
              ),
              _ReviewBar(
                label: "4",
                value: counterFourStars / counter,
                color: color,
                backgroundColor: backgroundColor,
              ),
              _ReviewBar(
                label: "3",
                value: counterThreeStars / counter,
                color: color,
                backgroundColor: backgroundColor,
              ),
              _ReviewBar(
                label: "2",
                value: counterTwoStars / counter,
                color: color,
                backgroundColor: backgroundColor,
              ),
              _ReviewBar(
                label: "1",
                value: counterOneStars / counter,
                color: color,
                backgroundColor: backgroundColor,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// A widget that displays a horizontal bar with a label.
///
/// Example:
/// ```dart
///  _ReviewBar(
///    label: "5",
///    value: counterFiveStars / counter,
///    color: color,
///    secondaryColor: secondaryColor,
///  )
/// ```
class _ReviewBar extends StatelessWidget {
  const _ReviewBar({
    required this.label,
    required this.value,
    this.color = Colors.amber,
    this.backgroundColor = const Color(0xFFEEEEEE),
  });

  /// The label of the bar.
  ///
  /// It will be displayed on the left side of the bar.
  final String label;

  /// The progress value of the bar.
  ///
  /// It must be between 0.0 and 1.0.
  final double value;

  /// The color of the bar.
  ///
  /// The default value is Colors.amber.
  final Color color;

  /// The backgroundcolor of the bar.
  ///
  /// The default value is Color(0xFFEEEEEE).
  final Color backgroundColor;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: SizedBox(
                height: 10,
                child: LinearProgressIndicator(
                  value: value,
                  valueColor: AlwaysStoppedAnimation<Color>(color),
                  backgroundColor: backgroundColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
