// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDOqxaip8INRseXD5riBxpnqo1xKSQrOEM',
    appId: '1:1026284748792:web:70766910d8b9d2b45da00d',
    messagingSenderId: '1026284748792',
    projectId: 'haimed-cendana',
    authDomain: 'haimed-cendana.firebaseapp.com',
    storageBucket: 'haimed-cendana.appspot.com',
    measurementId: 'G-WK9T2S4VSJ',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDE9Mfj4sNZdHaN22N8v6peWPbrmGLaTeQ',
    appId: '1:1026284748792:android:be3a0609d6d601d75da00d',
    messagingSenderId: '1026284748792',
    projectId: 'haimed-cendana',
    storageBucket: 'haimed-cendana.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDBPK43kkLLvN47STgXhEhrZTejtc0N4xo',
    appId: '1:1026284748792:ios:6347b8c59edb95925da00d',
    messagingSenderId: '1026284748792',
    projectId: 'haimed-cendana',
    storageBucket: 'haimed-cendana.appspot.com',
    iosClientId: '1026284748792-ojjf6k6cjek3jlvupfcibk4bp7vlchb3.apps.googleusercontent.com',
    iosBundleId: 'com.sanata.haimedGetx',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDBPK43kkLLvN47STgXhEhrZTejtc0N4xo',
    appId: '1:1026284748792:ios:6347b8c59edb95925da00d',
    messagingSenderId: '1026284748792',
    projectId: 'haimed-cendana',
    storageBucket: 'haimed-cendana.appspot.com',
    iosClientId: '1026284748792-ojjf6k6cjek3jlvupfcibk4bp7vlchb3.apps.googleusercontent.com',
    iosBundleId: 'com.sanata.haimedGetx',
  );
}
