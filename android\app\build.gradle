def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"
apply plugin: 'com.google.gms.google-services'

android {
    compileSdkVersion 34
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        // applicationId "com.sanata.haimed.cendana"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-build-configuration.
        minSdkVersion 25
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

        multiDexEnabled true
    }

    signingConfigs {
        cendana {
            def keystoreProperties = new Properties()
            def keystorePropertiesFile = rootProject.file("app/src/cendana/key.properties")
            keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? rootProject.file("app/src/cendana/${keystoreProperties['storeFile']}") : null
            storePassword keystoreProperties['storePassword']
        }

        premagana {
            def keystoreProperties = new Properties()
            def keystorePropertiesFile = rootProject.file("app/src/premagana/key.properties")
            keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? rootProject.file("app/src/premagana/${keystoreProperties['storeFile']}") : null
            storePassword keystoreProperties['storePassword']
        }

        debug {
            keyAlias "AndroidDebugKey"
            keyPassword "android"
            storeFile file("debug.keystore")
            storePassword "android"
        }
    }

    //flavor
    flavorDimensions "app"
    productFlavors {
        cendana {
            dimension "app"
            resValue "string", "app_name", "HaiMed RS Cendana"
            namespace "com.sanata.haimed.cendana"
            applicationId "com.sanata.haimed.cendana"
            versionName flutterVersionName
            // signingConfig signingConfigs.cendana
            //uncomment signingconfig above and comment signingconfig below for release per flavor
            signingConfig signingConfigs.debug
        }

        rsbk {
            dimension "app"
            resValue "string", "app_name", "SIPRAJA RS Bhayangkara Denpasar"
            namespace "com.haimed.rsbk"
            applicationId "com.haimed.rsbk"
            versionName flutterVersionName
            // signingConfig signingConfigs.rsbk
            //uncomment signingconfig above and comment signingconfig below for release per flavor
            signingConfig signingConfigs.debug
        }

        rsbkdev {
            dimension "app"
            resValue "string", "app_name", "Dev SIPRAJA"
            namespace "com.sanata.haimed.rsbk.dev"
            applicationId "com.sanata.haimed.rsbk.dev"
            versionName flutterVersionName
            signingConfig signingConfigs.debug
        }

        suwiti {
            dimension "app"
            resValue "string", "app_name", "HaiMed RSD Suwiti"
            namespace "com.sanata.haimed.rssuwiti"
            applicationId "com.sanata.haimed.rssuwiti"
            versionName flutterVersionName
            // signingConfig signingConfigs.suwiti
            //uncomment signingconfig above and comment signingconfig below for release per flavor
            signingConfig signingConfigs.debug
        }

        giriasih {
            dimension "app"
            resValue "string", "app_name", "HaiMed RSD Giri Asih"
            namespace "com.sanata.haimed.rsgiriasih"
            applicationId "com.sanata.haimed.rsgiriasih"
            versionName flutterVersionName
            // signingConfig signingConfigs.giriasih
            //uncomment signingconfig above and comment signingconfig below for release per flavor
            signingConfig signingConfigs.debug
        }

        premagana {
            dimension "app"
            resValue "string", "app_name", "HaiMed RS Premagana"
            namespace "com.sanata.haimed.premagana"
            applicationId "com.sanata.haimed.premagana"
            versionName flutterVersionName
            // signingConfig signingConfigs.premagana
            //uncomment signingconfig above and comment signingconfig below for release per flavor
            signingConfig signingConfigs.debug
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'com.android.support:multidex:1.0.3'
    implementation platform('com.google.firebase:firebase-bom:30.2.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.android.material:material:1.7.0'
    implementation 'androidx.multidex:multidex:2.0.1'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.1.5'
}
